# 实时客流统计功能测试

## 📋 测试文件说明

### 1. `test_api_server.py` - 模拟API服务器
- **功能**: 模拟sever_api.py的API端点
- **端口**: 5123
- **数据**: 每3秒自动更新模拟人数数据
- **端点**:
  - `GET /api/count` - 获取总人数
  - `GET /api/channels` - 获取各通道人数  
  - `GET /api/edges` - 获取各edge人数
  - `GET /api/status` - 获取服务状态

### 2. `test_api_client.py` - API测试客户端
- **功能**: 测试API端点是否正常工作
- **特性**: 可以实时监控数据变化

### 3. `run_test.bat` - 快速启动脚本
- **功能**: 一键启动测试环境

## 🚀 测试步骤

### 步骤1: 启动模拟API服务器
```bash
# 方法1: 直接运行
python test_api_server.py

# 方法2: 使用批处理文件
run_test.bat
```

### 步骤2: 测试API端点（可选）
```bash
# 在新的命令行窗口中运行
python test_api_client.py
```

### 步骤3: 启动前端测试
```bash
# 在前端项目目录中
npm run dev
```

### 步骤4: 查看效果
1. 打开浏览器访问 `http://localhost:3000`
2. 观察FlowChart组件是否显示真实数据
3. 检查浏览器控制台的日志输出

## 📊 预期效果

### API服务器输出
```
[14:30:15] 模拟数据更新 - 204-1: 3人, 204-2: 2人, 总计: 5人
[14:30:18] 模拟数据更新 - 204-1: 5人, 204-2: 1人, 总计: 6人
```

### 前端控制台输出
```
人数数据已更新: {edge:069: {person_count: 3}, edge:073: {person_count: 2}}
人数统计更新: 5人
FlowChart接收到人数数据: 5
```

### FlowChart显示
- 图表显示实时变化的人数数据
- 每3秒更新一次
- 数据范围: 0-14人（204-1: 0-8人 + 204-2: 0-6人）

## 🔧 故障排除

### 问题1: 端口5123被占用
```bash
# 查看端口占用
netstat -ano | findstr :5123

# 结束占用进程
taskkill /PID <进程ID> /F
```

### 问题2: 前端无法获取数据
- 检查API服务器是否正在运行
- 检查浏览器控制台是否有CORS错误
- 确认API地址是否正确

### 问题3: 数据不更新
- 检查MapPanel组件是否正确启动人数统计
- 查看浏览器控制台的错误信息
- 确认数据流路径是否正确

## 📝 测试检查清单

- [ ] API服务器成功启动
- [ ] 所有API端点返回正确数据
- [ ] 前端成功获取API数据
- [ ] MapPanel正确更新edge人数
- [ ] CenterPanel正确传递数据
- [ ] FlowChart显示真实人数变化
- [ ] 数据每3秒自动更新

## 🎯 成功标准

✅ **测试成功的标志**:
1. API服务器正常运行，控制台显示数据更新
2. 前端FlowChart显示变化的人数数据
3. 浏览器控制台显示正确的数据流日志
4. 图表每3秒自动更新，不再显示模拟的固定模式

如果以上条件都满足，说明实时客流统计功能已经成功打通！
