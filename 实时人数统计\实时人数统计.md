# 实时人数统计系统设计需求文档

## 📋 项目概述

**核心目标**: 让FlowChart.vue组件显示真实的人数统计数据，替换当前的模拟数据。

## 🎯 核心需求

### 唯一目标
- **FlowChart.vue显示真实人数** - 将模拟数据替换为基于监控通道的真实统计

### 统计逻辑
- **客流统计针对block** - 最终统计结果以block为单位
- **block通过edge获得人数** - block人数来源于其绑定的edge
- **edge人数=监控人数** - 有监控的edge用监控数据，无监控的edge为0
- **belonging_block规则**:
  - `null`: 不属于任何block，跳过
  - `单个ID`: 人数累加到该block
  - `[多个ID]`: 人数累加到所有block

### 数据源
- **视频通道**: 204-1（大屏端）、204-2（出入口）
- **目标房间**: 204房间总人数 = 204-1 + 204-2

## 🏗️ 技术架构

### 数据流设计
```
视频监控API → Edge人数分配 → belonging_block聚合 → 204房间总人数 → FlowChart.vue
```

### 核心组件
- **FlowChart.vue** - 实时客流统计图表（需要接入真实数据）
- **人数统计服务** - 数据获取和处理逻辑

## 📊 数据结构设计

### 1. Edge到监控通道的映射
```javascript
const EDGE_TO_CHANNEL_MAPPING = {
  'edge:069': '204-1',  // 大屏端
  'edge:073': '204-2'   // 出入口
  // 其他edge无监控，人数为0
};
```

### 2. API数据格式
```javascript
// 当前API: /api/count 返回总数
// 需要扩展为多通道API
{
  "204-1": 5,  // 大屏端人数
  "204-2": 3   // 出入口人数
}
```

## 🎨 FlowChart组件现状

### 已完成功能
- ✅ 24小时制时间轴
- ✅ 实时滚动折线图
- ✅ 每3秒更新数据点
- ✅ 面积阴影和点标记
- ✅ 开始/停止控制

### 需要修改
- ❌ 当前使用模拟数据 `getPersonCount()`
- ✅ 需要替换为真实的204房间人数统计

## 🔧 实现方案

### 核心任务
**修改FlowChart.vue中的`getPersonCount()`函数，从模拟数据改为真实统计**

### 实现步骤
1. **获取监控数据**: 调用API获取204-1和204-2的人数
2. **计算204房间总人数**: 204-1 + 204-2
3. **替换模拟数据**: 在FlowChart.vue中使用真实数据

### 核心代码修改
```javascript
// 在FlowChart.vue中修改这个函数
// 原来的模拟数据函数
const getPersonCount = () => {
  const baseCount = 8;
  const variation = Math.floor(Math.random() * 12) - 6;
  return Math.max(0, Math.min(20, baseCount + variation));
};

// 修改为真实数据函数 - 简化方案
const getPersonCount = async () => {
  try {
    // 直接调用现有API获取总人数
    const response = await fetch('http://localhost:5123/api/count');
    const data = await response.json();

    // 简单方案：直接使用API返回的总人数
    // 假设这个总人数就是204房间的人数（204-1 + 204-2）
    return data.count || 0;

  } catch (error) {
    console.error('获取204房间人数失败:', error);
    return 0; // 失败时返回0，或者返回模拟数据作为降级
  }
};
```

### 实现说明
- **最简方案**：直接使用现有的`/api/count`接口
- **假设前提**：该接口返回的就是204房间的总人数
- **无需复杂映射**：不需要通道发现、不需要block聚合
- **快速实现**：只需修改一个函数，立即可用

## � 实施计划

### 唯一任务: FlowChart真实数据接入

#### 步骤1: API数据获取
- 确认现有`/api/count`接口返回格式
- 如需要，扩展API支持204-1和204-2分别获取
- 或实现基于block统计的聚合逻辑

#### 步骤2: 修改FlowChart.vue
- 将`getPersonCount()`从模拟数据改为API调用
- 确保异步数据获取正常工作
- 保持现有的图表显示功能

#### 步骤3: 测试验证
- 验证真实数据显示正确
- 确保图表更新正常
- 处理API失败的降级方案

## 📋 验收标准

### 唯一验收目标
- [ ] **FlowChart显示真实数据**: 图表显示基于204-1和204-2的真实人数统计
- [ ] **数据更新正常**: 每3秒正确获取和显示最新人数
- [ ] **错误处理**: API失败时有合理的降级处理

---

## 📝 总结

**核心任务**: 修改FlowChart.vue的`getPersonCount()`函数，从模拟数据改为真实的204房间人数统计。

**实现方式**: 非侵入式，不修改地图文件，仅在FlowChart组件中接入真实数据源。

**成功标准**: FlowChart图表显示真实的204房间人数变化趋势。
