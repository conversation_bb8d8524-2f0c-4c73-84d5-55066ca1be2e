<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue';
import * as echarts from 'echarts';

// 图表实例
const flowChartRef = ref(null);
let flowChartInstance = null;
const isUpdating = ref(false); // 响应式的更新状态
let dataUpdateInterval = null;

// 实时数据存储：时间点和对应人数
const flowData = ref([]);
const maxDisplayPoints = 30; // 显示最近30个数据点，便于观察滚动效果

// 从MapPanel接收的人数数据
const latestPersonCount = ref(0);

// 区域选择相关状态
const availableBlocks = ref([]);
const selectedBlock = ref('大屏端');

// 获取当前精确时间字符串
const getCurrentTimeString = () => {
  const now = new Date();
  const hours = now.getHours().toString().padStart(2, '0');
  const minutes = now.getMinutes().toString().padStart(2, '0');
  const seconds = now.getSeconds().toString().padStart(2, '0');
  return `${hours}:${minutes}:${seconds}`;
};

// 添加新的数据点（每次都是新的时间点）
const addDataPoint = (personCount) => {
  const currentTime = getCurrentTimeString();

  // 直接添加新数据点（每次都是新时间）
  flowData.value.push({
    time: currentTime,
    value: personCount
  });

  // 保持数据点数量在合理范围内
  if (flowData.value.length > maxDisplayPoints) {
    flowData.value.shift(); // 移除最旧的数据点
  }
};

// 获取真实的人数数据（从MapPanel获取）
const getPersonCount = () => {
  // 直接使用从MapPanel接收的最新人数数据
  return latestPersonCount.value;
};

// 实时客流统计图表初始化
const initFlowChart = () => {
  if (flowChartInstance) {
    flowChartInstance.dispose();
  }
  flowChartInstance = echarts.init(flowChartRef.value);

  const option = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#0184d5',
      borderWidth: 1,
      textStyle: {
        color: '#fff',
        fontSize: 12
      },
      formatter: function(params) {
        if (params && params.length > 0) {
          const data = params[0];
          return `时间: ${data.name}<br/>人数: ${data.value}人`;
        }
        return '';
      }
    },
    grid: {
      top: '8%',
      bottom: '15%',
      left: '8%',
      right: '5%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: flowData.value.map(item => item.time),
      axisLine: {
        lineStyle: { color: '#ffffff33' }
      },
      axisTick: {
        show: true,
        lineStyle: { color: '#ffffff33' }
      },
      axisLabel: {
        color: '#ccc',
        fontSize: 10,
        interval: function(index) {
          // 每隔10个数据点显示一个标签，避免拥挤
          return index % 10 === 0;
        },
        rotate: 0,
        formatter: function(value) {
          // 显示时:分格式，去掉秒
          if (value && value.includes(':')) {
            const parts = value.split(':');
            return `${parts[0]}:${parts[1]}`;
          }
          return value;
        }
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#ffffff11',
          type: 'dashed'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: '人数',
      nameTextStyle: {
        color: '#ccc',
        fontSize: 11
      },
      min: 0,
      max: 25,
      interval: 5,
      axisLine: {
        show: true,
        lineStyle: { color: '#ffffff33' }
      },
      axisTick: {
        show: true,
        lineStyle: { color: '#ffffff33' }
      },
      splitLine: {
        lineStyle: {
          color: '#ffffff22',
          type: 'dashed'
        }
      },
      axisLabel: {
        color: '#ccc',
        fontSize: 10,
        formatter: '{value}'
      }
    },
    series: [
      {
        name: '实时人数',
        type: 'line',
        data: flowData.value.map(item => item.value),
        smooth: false,
        symbol: 'circle',
        symbolSize: 5,
        itemStyle: {
          color: '#0184d5',
          borderColor: '#ffffff',
          borderWidth: 1
        },
        lineStyle: {
          color: '#0184d5',
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              {offset: 0, color: 'rgba(1, 132, 213, 0.4)'},
              {offset: 0.5, color: 'rgba(1, 132, 213, 0.2)'},
              {offset: 1, color: 'rgba(1, 132, 213, 0.05)'}
            ]
          }
        },
        emphasis: {
          focus: 'series',
          itemStyle: {
            color: '#40a9ff',
            borderColor: '#ffffff',
            borderWidth: 2,
            shadowBlur: 8,
            shadowColor: '#0184d5'
          }
        }
      }
    ],
    animation: true,
    animationDuration: 300,
    animationEasing: 'cubicOut'
  };

  flowChartInstance.setOption(option);
};

// 开始实时数据更新
const startRealTimeUpdate = () => {
  if (dataUpdateInterval) return;

  console.log('开始实时更新');
  isUpdating.value = true;
  dataUpdateInterval = setInterval(() => {
    try {
      // 获取真实的人数数据
      const newPersonCount = getPersonCount();

      // 添加新数据点
      addDataPoint(newPersonCount);

      // 更新图表
      if (flowChartInstance) {
        flowChartInstance.setOption({
          xAxis: {
            data: flowData.value.map(item => item.time)
          },
          series: [{
            data: flowData.value.map(item => item.value)
          }]
        });
      }

      emit('data-updated', {
        time: getCurrentTimeString(),
        count: newPersonCount,
        totalPoints: flowData.value.length
      });
    } catch (error) {
      console.error('更新人数数据失败:', error);
    }
  }, 3000); // 每3秒更新一次
};

// 停止实时数据更新
const stopRealTimeUpdate = () => {
  if (dataUpdateInterval) {
    console.log('停止实时更新');
    clearInterval(dataUpdateInterval);
    dataUpdateInterval = null;
    isUpdating.value = false;
  }
};

// 重置图表数据
const resetChart = () => {
  flowData.value = [];
  if (flowChartInstance) {
    flowChartInstance.setOption({
      xAxis: {
        data: []
      },
      series: [{
        data: []
      }]
    });
  }
  console.log('图表数据已重置');
};

// 设置图表数据
const setChartData = (newData) => {
  if (Array.isArray(newData) && newData.length > 0) {
    flowData.value = [...newData];
    if (flowChartInstance) {
      flowChartInstance.setOption({
        xAxis: {
          data: flowData.value.map(item => item.time || item)
        },
        series: [{
          data: flowData.value.map(item => item.value || item)
        }]
      });
    }
  }
};

// 接收来自MapPanel的人数数据
const updatePersonCount = (count) => {
  latestPersonCount.value = count;
  console.log('FlowChart接收到人数数据:', count);
};

// 更新可用区域列表
const updateAvailableBlocks = (blocks) => {
  availableBlocks.value = blocks;
  console.log('FlowChart接收到可用区域列表:', blocks);

  // 如果当前选择的区域不在新列表中，选择第一个可用区域
  if (blocks.length > 0 && !blocks.find(block => block.name === selectedBlock.value)) {
    selectedBlock.value = blocks[0].name;
    handleBlockSelectionChange();
  }
};

// 处理区域选择变更
const handleBlockSelectionChange = () => {
  console.log('区域选择变更为:', selectedBlock.value);
  emit('block-selection-changed', selectedBlock.value);
};

// 暴露事件给父组件
const emit = defineEmits(['data-updated', 'chart-clicked', 'block-selection-changed']);

// 可以通过props接收配置
const props = defineProps({
  autoStart: {
    type: Boolean,
    default: true
  },
  updateInterval: {
    type: Number,
    default: 1000
  },
  maxValue: {
    type: Number,
    default: 10
  },
  initialData: {
    type: Array,
    default: () => []
  },
  availableBlocks: {
    type: Array,
    default: () => []
  }
});

// 暴露方法给父组件
defineExpose({
  startRealTimeUpdate,
  stopRealTimeUpdate,
  resetChart,
  setChartData,
  updatePersonCount,
  updateAvailableBlocks,
  getCurrentData: () => flowData.value,
  getSelectedBlock: () => selectedBlock.value
});

// 监听props变化
watch(() => props.autoStart, (newVal) => {
  if (newVal) {
    startRealTimeUpdate();
  } else {
    stopRealTimeUpdate();
  }
}, { immediate: true });

// 监听可用区域列表变化
watch(() => props.availableBlocks, (newBlocks) => {
  if (newBlocks && newBlocks.length > 0) {
    updateAvailableBlocks(newBlocks);
  }
}, { immediate: true, deep: true });

// 组件挂载
onMounted(() => {
  nextTick(() => {
    // 初始化空数据
    flowData.value = [];

    // 如果有初始数据，使用初始数据
    if (props.initialData && props.initialData.length > 0) {
      flowData.value = [...props.initialData];
    }

    initFlowChart();

    // 监听窗口大小变化
    const resizeHandler = () => {
      if (flowChartInstance) {
        flowChartInstance.resize();
      }
    };

    window.addEventListener('resize', resizeHandler);

    // 如果设置了自动开始
    if (props.autoStart) {
      console.log('自动开始实时更新');
      startRealTimeUpdate();
    }

    // 添加图表点击事件
    if (flowChartInstance) {
      flowChartInstance.on('click', (params) => {
        emit('chart-clicked', params);
      });
    }

    // 保存清理函数
    onUnmounted(() => {
      window.removeEventListener('resize', resizeHandler);
      stopRealTimeUpdate();
      if (flowChartInstance) {
        flowChartInstance.dispose();
      }
    });
  });
});
</script>

<template>
  <div class="panel-section flow-statistics">
    <div class="chart-title">
      <span style="width: 3px; height: 16px; background-color: #2997e3; margin-right: 8px;"></span>
      实时客流统计
      <div class="chart-controls">
        <select
          v-model="selectedBlock"
          @change="handleBlockSelectionChange"
          class="block-selector"
          title="选择监控区域"
        >
          <option v-for="block in availableBlocks" :key="block.id" :value="block.name">
            {{ block.name }}
          </option>
        </select>
        <button
          class="control-btn"
          :class="{ active: isUpdating }"
          @click="isUpdating ? stopRealTimeUpdate() : startRealTimeUpdate()"
        >
          {{ isUpdating ? '停止' : '开始' }}
        </button>
        <button
          class="control-btn"
          @click="resetChart"
        >
          重置
        </button>
      </div>
    </div>
    <div class="chart-container">
      <div ref="flowChartRef" style="width: 100%; height: 100%;"></div>
    </div>
  </div>
</template>

<style scoped>
/* 面板通用样式 */
.panel-section {
  margin-bottom: 5px;
  border: 1px solid #1e3a5f;
  border-radius: 4px;
  background-color: #0d2b55;
  height: 100%;
}

.chart-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 12px;
  font-size: 0.9rem;
  font-weight: bold;
  color: white;
}

.chart-controls {
  display: flex;
  gap: 8px;
  align-items: center;
}

.block-selector {
  padding: 4px 8px;
  border: 1px solid #3a4a5c;
  background: rgba(58, 74, 92, 0.8);
  color: #ffffff;
  border-radius: 3px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
}

.block-selector:hover {
  background: rgba(58, 74, 92, 1);
  border-color: #1890ff;
}

.block-selector:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.block-selector option {
  background: #2a3441;
  color: #ffffff;
  padding: 4px 8px;
}

.control-btn {
  background-color: #1890ff;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 3px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.control-btn:hover {
  background-color: #40a9ff;
  transform: translateY(-1px);
}

.control-btn.active {
  background-color: #52c41a;
  box-shadow: 0 0 8px rgba(82, 196, 26, 0.3);
}

.chart-container {
  height: calc(100% - 40px);
  padding: 10px;
  overflow: hidden;
}

/* 实时客流统计图表样式 */
.flow-statistics {
  flex: 1;
  height: 100%;
}
</style>
