<script setup>
import { ref } from 'vue';
import MapPanel from './MapPanel.vue';
import EvacuationChart from './EvacuationChart.vue';
import FlowChart from './FlowChart.vue';

// 地图相关数据
const currentFloor = ref('B1');
const mapScale = ref(1);

// 图表相关数据
const evacuationChartRef = ref(null);
const flowChartRef = ref(null);

// 人数统计数据
const currentPersonCount = ref(0);
const personCountData = ref({});

// 区域选择相关状态
const availableBlocks = ref([]);
const mapPanelRef = ref(null);

// 处理地图事件
const handleFloorChanged = (floor) => {
  currentFloor.value = floor;
  emit('floor-changed', floor);
  console.log(`楼层切换到: ${floor}`);
};

const handleZoomChanged = (scale) => {
  mapScale.value = scale;
  emit('zoom-changed', scale);
};

const handleMapControlChanged = (controlData) => {
  emit('map-control-changed', controlData);
  console.log(`地图控制变更:`, controlData);
};

const handleMapTitleUpdated = (title) => {
  emit('map-title-updated', title);
  console.log(`地图标题更新:`, title);
};

// 处理人数统计事件
const handlePersonCountUpdated = (data) => {
  currentPersonCount.value = data.totalCount;
  personCountData.value = data;

  // 将人数数据传递给FlowChart
  if (flowChartRef.value) {
    flowChartRef.value.updatePersonCount(data.totalCount);
  }

  console.log(`区域 "${data.selectedBlock}" 人数统计更新: ${data.totalCount}人`, data);
};

// 处理可用区域列表更新
const handleAvailableBlocksUpdated = (blocks) => {
  availableBlocks.value = blocks;

  // 将区域列表传递给FlowChart
  if (flowChartRef.value) {
    flowChartRef.value.updateAvailableBlocks(blocks);
  }

  console.log('可用区域列表更新:', blocks);
};

// 处理区域选择变更
const handleBlockSelectionChanged = (blockName) => {
  // 通知MapPanel更新选择的区域
  if (mapPanelRef.value) {
    mapPanelRef.value.handleBlockSelectionChange(blockName);
  }

  console.log(`区域选择变更为: ${blockName}`);
};

// 处理疏散图表事件
const handleEvacuationSimulationComplete = (data) => {
  emit('evacuation-simulation-complete', data);
  console.log('疏散模拟完成:', data);
};

const handleEvacuationDataUpdated = (data) => {
  emit('evacuation-data-updated', data);
};

// 处理客流图表事件
const handleFlowDataUpdated = (data) => {
  emit('flow-data-updated', data);
};

const handleFlowChartClicked = (params) => {
  emit('flow-chart-clicked', params);
  console.log('客流图表点击:', params);
};

// 开始疏散模拟
const startEvacuationSimulation = () => {
  if (evacuationChartRef.value) {
    evacuationChartRef.value.startSimulation();
  }
};

// 清空疏散图表
const clearEvacuationChart = () => {
  if (evacuationChartRef.value) {
    evacuationChartRef.value.clearChart();
  }
};

// 控制客流图表
const startFlowChart = () => {
  if (flowChartRef.value) {
    flowChartRef.value.startRealTimeUpdate();
  }
};

const stopFlowChart = () => {
  if (flowChartRef.value) {
    flowChartRef.value.stopRealTimeUpdate();
  }
};

const resetFlowChart = () => {
  if (flowChartRef.value) {
    flowChartRef.value.resetChart();
  }
};

// 暴露事件给父组件
const emit = defineEmits([
  'floor-changed',
  'zoom-changed',
  'map-control-changed',
  'evacuation-simulation-complete',
  'evacuation-data-updated',
  'flow-data-updated',
  'flow-chart-clicked',
  'map-title-updated'
]);

// 可以通过props接收配置
const props = defineProps({
  initialFloor: {
    type: String,
    default: 'B1'
  },
  initialScale: {
    type: Number,
    default: 1
  },
  enableMapDrag: {
    type: Boolean,
    default: true
  },
  autoStartFlowChart: {
    type: Boolean,
    default: true
  },
  autoStartEvacuation: {
    type: Boolean,
    default: false
  }
});

// 使用props初始化
if (props.initialFloor) currentFloor.value = props.initialFloor;
if (props.initialScale) mapScale.value = props.initialScale;

// 暴露方法给父组件
defineExpose({
  startEvacuationSimulation,
  clearEvacuationChart,
  startFlowChart,
  stopFlowChart,
  resetFlowChart,
  getCurrentFloor: () => currentFloor.value,
  getCurrentScale: () => mapScale.value
});
</script>

<template>
  <div class="center-panel">
    <!-- 4. 地图显示 -->
    <MapPanel
      ref="mapPanelRef"
      :initial-floor="props.initialFloor"
      :initial-scale="props.initialScale"
      :enable-drag="props.enableMapDrag"
      @floor-changed="handleFloorChanged"
      @zoom-changed="handleZoomChanged"
      @map-control-changed="handleMapControlChanged"
      @update:mapTitle="handleMapTitleUpdated"
      @person-count-updated="handlePersonCountUpdated"
      @available-blocks-updated="handleAvailableBlocksUpdated"
      @selected-block-changed="handleBlockSelectionChanged"
    />

    <!-- 底部图表区域 -->
    <div class="bottom-charts">
      <!-- 5. 疏散模拟结果 -->
      <EvacuationChart 
        ref="evacuationChartRef"
        :auto-start="props.autoStartEvacuation"
        @simulation-complete="handleEvacuationSimulationComplete"
        @data-updated="handleEvacuationDataUpdated"
      />

      <!-- 6. 实时客流统计 -->
      <FlowChart
        ref="flowChartRef"
        :auto-start="props.autoStartFlowChart"
        :available-blocks="availableBlocks"
        @data-updated="handleFlowDataUpdated"
        @chart-clicked="handleFlowChartClicked"
        @block-selection-changed="handleBlockSelectionChanged"
      />
    </div>
  </div>
</template>

<style scoped>
/* 中间面板样式 */
.center-panel {
  flex: 1;
  padding: 5px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 底部图表区域样式 */
.bottom-charts {
  display: flex;
  gap: 10px;
  overflow: hidden;
  height: 250px;
  margin-top: 5px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .bottom-charts {
    height: 200px;
  }
}

@media (max-width: 768px) {
  .center-panel {
    padding: 2px;
  }
  
  .bottom-charts {
    flex-direction: column;
    height: auto;
    gap: 5px;
  }
  
  .bottom-charts > * {
    height: 150px;
  }
}

@media (max-width: 480px) {
  .bottom-charts > * {
    height: 120px;
  }
}
</style>
