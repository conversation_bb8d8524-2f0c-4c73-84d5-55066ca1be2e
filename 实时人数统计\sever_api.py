from flask import Flask, jsonify, request
from threading import Thread, Lock
import cv2
from ultralytics import YOLO
import torch
import time
from flask_cors import CORS
import os
import csv
from datetime import datetime

app = Flask(__name__)
CORS(app)  # 启用跨域支持

# 全局变量 - 多通道支持
channels = {
    '204-1': {
        'rtsp_url': 'rtsp://admin:hucom12345@10.10.52.37:554/Streaming/Channels/1501',
        'person_count': 0,
        'detection_running': False,
        'detection_thread': None,
        'edge_id': 'edge:069'  # 对应的edge ID
    },
    '204-2': {
        'rtsp_url': 'rtsp://admin:hucom12345@10.10.52.37:554/Streaming/Channels/1601',
        'person_count': 0,
        'detection_running': False,
        'detection_thread': None,
        'edge_id': 'edge:073'  # 对应的edge ID
    }
}
channels_lock = Lock()  # 用于线程安全的通道更新

# 保存CSV路径和文件夹 - 使用相对路径
csv_dir = os.path.join(os.path.dirname(__file__), "count")
os.makedirs(csv_dir, exist_ok=True)

def get_today_csv_path():
    today_str = datetime.now().strftime("%Y-%m-%d")
    return os.path.join(csv_dir, f"{today_str}.csv")

# 初始化当天CSV文件，写入表头（如果文件不存在）
if not os.path.exists(get_today_csv_path()):
    with open(get_today_csv_path(), 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(['Time', 'Count'])

def save_count_to_csv(time_str, count_info):
    csv_path = get_today_csv_path()
    with open(csv_path, 'a', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow([time_str, count_info])

@app.route("/api/count")
def get_count():
    """返回总人数（兼容旧接口）"""
    with channels_lock:
        total_count = sum(channel['person_count'] for channel in channels.values())
        any_running = any(channel['detection_running'] for channel in channels.values())

    return jsonify({
        "count": total_count,
        "status": "running" if any_running else "stopped"
    })

@app.route("/api/channels")
def get_channels():
    """返回各通道详细信息"""
    with channels_lock:
        channel_data = {}
        for channel_id, info in channels.items():
            channel_data[channel_id] = {
                'count': info['person_count'],
                'status': 'running' if info['detection_running'] else 'stopped',
                'rtsp_url': info['rtsp_url']
            }

    return jsonify({
        'channels': channel_data,
        'total_count': sum(data['count'] for data in channel_data.values())
    })

@app.route("/api/edges")
def get_edges():
    """返回edge级别的人数数据"""
    with channels_lock:
        edge_data = {}
        for channel_id, info in channels.items():
            edge_id = info['edge_id']
            edge_data[edge_id] = {
                'person_count': info['person_count'],
                'channel_id': channel_id,
                'status': 'running' if info['detection_running'] else 'stopped'
            }

    return jsonify({
        'edges': edge_data,
        'total_count': sum(data['person_count'] for data in edge_data.values())
    })

@app.route("/api/set_rtsp", methods=['POST'])
def set_rtsp_url():
    """设置特定通道的RTSP URL（保留兼容性）"""
    data = request.get_json()
    if not data or 'rtsp_url' not in data:
        return jsonify({"error": "Missing rtsp_url parameter"}), 400

    new_url = data['rtsp_url']
    if not new_url.startswith('rtsp://'):
        return jsonify({"error": "Invalid RTSP URL format"}), 400

    # 根据URL判断是哪个通道
    channel_id = None
    if '1501' in new_url:
        channel_id = '204-1'
    elif '1601' in new_url:
        channel_id = '204-2'

    if not channel_id:
        return jsonify({"error": "Unknown channel URL"}), 400

    with channels_lock:
        channels[channel_id]['rtsp_url'] = new_url

        # 重启该通道的检测线程
        if channels[channel_id]['detection_running']:
            channels[channel_id]['detection_running'] = False
            if channels[channel_id]['detection_thread'] and channels[channel_id]['detection_thread'].is_alive():
                channels[channel_id]['detection_thread'].join(timeout=3)

        # 启动新的检测线程
        channels[channel_id]['detection_running'] = True
        channels[channel_id]['detection_thread'] = Thread(target=detection_loop, args=(channel_id,), daemon=True)
        channels[channel_id]['detection_thread'].start()

    return jsonify({
        "message": f"Channel {channel_id} RTSP URL updated successfully",
        "channel_id": channel_id,
        "rtsp_url": new_url,
        "status": "running"
    })

@app.route("/api/start_detection", methods=['POST'])
def start_detection():
    """启动所有通道的检测"""
    with channels_lock:
        started_channels = []
        for channel_id, info in channels.items():
            if not info['detection_running']:
                info['detection_running'] = True
                info['detection_thread'] = Thread(target=detection_loop, args=(channel_id,), daemon=True)
                info['detection_thread'].start()
                started_channels.append(channel_id)

    if started_channels:
        return jsonify({
            "message": f"Detection started for channels: {', '.join(started_channels)}",
            "status": "running",
            "channels": started_channels
        })
    else:
        return jsonify({"message": "All channels already running", "status": "running"})

@app.route("/api/stop_detection", methods=['POST'])
def stop_detection():
    """停止所有通道的检测"""
    with channels_lock:
        stopped_channels = []
        for channel_id, info in channels.items():
            if info['detection_running']:
                info['detection_running'] = False
                stopped_channels.append(channel_id)

    if stopped_channels:
        return jsonify({
            "message": f"Detection stopped for channels: {', '.join(stopped_channels)}",
            "status": "stopped",
            "channels": stopped_channels
        })
    else:
        return jsonify({"message": "All channels already stopped", "status": "stopped"})

def show_frame_with_count(frame, count, width=640, height=480):
    cv2.namedWindow('YOLO Person Detection', cv2.WINDOW_NORMAL)
    cv2.resizeWindow('YOLO Person Detection', width, height)

    resized_frame = cv2.resize(frame, (width, height))
    cv2.putText(resized_frame, f'Persons: {count}', (20, 50),
                cv2.FONT_HERSHEY_SIMPLEX, 1.5, (0, 255, 0), 3)
    cv2.imshow('YOLO Person Detection', resized_frame)

def detection_loop(channel_id):
    """单个通道的检测循环"""
    model = YOLO('1000time.pt')
    model.to('cuda' if torch.cuda.is_available() else 'cpu')

    cap = None
    frame_count = 0
    last_count = 0
    reconnect_attempts = 0
    max_reconnect_attempts = 5

    show_window = False  # 【控制窗口显示，改 True 显示】

    print(f"开始检测通道 {channel_id}")

    while True:
        # 检查该通道是否还在运行
        with channels_lock:
            if not channels[channel_id]['detection_running']:
                break
            url = channels[channel_id]['rtsp_url']

        # 检查是否需要重新连接
        if cap is None:
            print(f"连接RTSP流 {channel_id}: {url}")
            cap = cv2.VideoCapture(url)

            if not cap.isOpened():
                print(f"无法连接RTSP流 {channel_id}: {url}")
                reconnect_attempts += 1
                if reconnect_attempts >= max_reconnect_attempts:
                    print(f"通道 {channel_id} 达到最大重连次数({max_reconnect_attempts})，停止检测")
                    break
                time.sleep(5)
                continue
            else:
                reconnect_attempts = 0
                print(f"通道 {channel_id} RTSP流连接成功: {url}")

        success, frame = cap.read()
        if not success:
            print(f"通道 {channel_id} 视频帧读取失败，尝试重连RTSP...")
            if cap:
                cap.release()
                cap = None
            time.sleep(3)
            continue

        frame_count += 1

        # 每15帧进行一次检测
        if frame_count == 1 or frame_count == 15:
            try:
                results = model.predict(frame, classes=[0])
                boxes = results[0].boxes.xyxy
                person_count = len(boxes)

                # 更新该通道的人数
                with channels_lock:
                    channels[channel_id]['person_count'] = person_count

                last_count = person_count

                if show_window:
                    annotated_frame = results[0].plot()
                    display_frame = annotated_frame
                else:
                    display_frame = frame

                # 保存检测时间和人数
                now_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                save_count_to_csv(now_time, f"{channel_id}:{last_count}")

                print(f"通道 {channel_id} 检测到人数: {person_count}, 时间: {now_time}")

            except Exception as e:
                print(f"通道 {channel_id} 检测过程中出现错误: {e}")
                display_frame = frame
        else:
            display_frame = frame

        if show_window:
            show_frame_with_count(display_frame, last_count, 1200, 800)
            if cv2.waitKey(1) & 0xFF == ord('q'):
                with channels_lock:
                    channels[channel_id]['detection_running'] = False
                break

        frame_count = frame_count % 30

        # 短暂休眠以减少CPU使用率
        time.sleep(0.03)  # 约30fps

    # 清理资源
    if cap:
        cap.release()
    if show_window:
        cv2.destroyAllWindows()

    print(f"通道 {channel_id} 检测循环结束")

if __name__ == '__main__':
    # 自动启动所有通道的检测
    print("启动所有通道的检测...")
    with channels_lock:
        for channel_id, info in channels.items():
            info['detection_running'] = True
            info['detection_thread'] = Thread(target=detection_loop, args=(channel_id,), daemon=True)
            info['detection_thread'].start()
            print(f"通道 {channel_id} 检测已启动: {info['rtsp_url']}")

    print("YOLOv8人员识别API服务启动")
    print("已启动的通道:")
    for channel_id, info in channels.items():
        print(f"  {channel_id}: {info['rtsp_url']} -> {info['edge_id']}")

    print("\nAPI端点:")
    print("  GET  /api/count - 获取总人数")
    print("  GET  /api/channels - 获取各通道人数")
    print("  GET  /api/edges - 获取各edge人数")
    print("  POST /api/set_rtsp - 设置RTSP URL")
    print("  POST /api/start_detection - 开始检测")
    print("  POST /api/stop_detection - 停止检测")

    # 使用端口5123避免与其他服务冲突
    app.run(host='0.0.0.0', port=5123, debug=False)
