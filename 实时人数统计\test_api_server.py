#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模拟人数识别API测试服务器
用于测试实时客流统计功能
"""

from flask import Flask, jsonify
from flask_cors import CORS
import random
import time
from datetime import datetime
import threading

app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 模拟的通道数据
channels_data = {
    '204-1': {
        'person_count': 0,
        'status': 'running',
        'rtsp_url': 'rtsp://admin:hucom12345@10.10.52.37:554/Streaming/Channels/1501',
        'edge_id': 'edge:069'
    },
    '204-2': {
        'person_count': 0,
        'status': 'running', 
        'rtsp_url': 'rtsp://admin:hucom12345@10.10.52.37:554/Streaming/Channels/1601',
        'edge_id': 'edge:073'
    }
}

# 模拟数据更新线程
update_running = True

def simulate_person_count_updates():
    """模拟人数数据的实时更新"""
    global update_running
    
    while update_running:
        # 模拟204-1通道人数变化（0-8人）
        channels_data['204-1']['person_count'] = random.randint(0, 8)
        
        # 模拟204-2通道人数变化（0-6人）
        channels_data['204-2']['person_count'] = random.randint(0, 6)
        
        # 打印当前模拟数据
        total = channels_data['204-1']['person_count'] + channels_data['204-2']['person_count']
        current_time = datetime.now().strftime("%H:%M:%S")
        print(f"[{current_time}] 模拟数据更新 - 204-1: {channels_data['204-1']['person_count']}人, "
              f"204-2: {channels_data['204-2']['person_count']}人, 总计: {total}人")
        
        # 每3秒更新一次
        time.sleep(3)

@app.route("/api/count")
def get_count():
    """返回总人数（兼容旧接口）"""
    total_count = sum(channel['person_count'] for channel in channels_data.values())
    
    return jsonify({
        "count": total_count,
        "status": "running"
    })

@app.route("/api/channels")
def get_channels():
    """返回各通道详细信息"""
    channel_response = {}
    for channel_id, info in channels_data.items():
        channel_response[channel_id] = {
            'count': info['person_count'],
            'status': info['status'],
            'rtsp_url': info['rtsp_url']
        }
    
    total_count = sum(data['count'] for data in channel_response.values())
    
    return jsonify({
        'channels': channel_response,
        'total_count': total_count
    })

@app.route("/api/edges")
def get_edges():
    """返回edge级别的人数数据"""
    edge_response = {}
    for channel_id, info in channels_data.items():
        edge_id = info['edge_id']
        edge_response[edge_id] = {
            'person_count': info['person_count'],
            'channel_id': channel_id,
            'status': info['status']
        }
    
    total_count = sum(data['person_count'] for data in edge_response.values())
    
    return jsonify({
        'edges': edge_response,
        'total_count': total_count
    })

@app.route("/api/status")
def get_status():
    """返回服务状态"""
    return jsonify({
        "service": "模拟人数识别API",
        "status": "running",
        "channels": list(channels_data.keys()),
        "timestamp": datetime.now().isoformat()
    })

@app.route("/")
def index():
    """首页，显示API信息"""
    return """
    <h1>模拟人数识别API测试服务器</h1>
    <h2>可用端点：</h2>
    <ul>
        <li><a href="/api/count">/api/count</a> - 获取总人数</li>
        <li><a href="/api/channels">/api/channels</a> - 获取各通道人数</li>
        <li><a href="/api/edges">/api/edges</a> - 获取各edge人数</li>
        <li><a href="/api/status">/api/status</a> - 获取服务状态</li>
    </ul>
    <p>服务运行在端口 5123，每3秒自动更新模拟数据</p>
    """

def signal_handler(signum, frame):
    """处理退出信号"""
    global update_running
    print("\n正在停止模拟数据更新...")
    update_running = False

if __name__ == "__main__":
    import signal
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    print("=" * 60)
    print("🚀 模拟人数识别API测试服务器启动")
    print("=" * 60)
    print("📡 服务地址: http://localhost:5123")
    print("📊 模拟通道:")
    for channel_id, info in channels_data.items():
        print(f"   {channel_id}: {info['rtsp_url']} -> {info['edge_id']}")
    
    print("\n🔗 API端点:")
    print("   GET  /api/count    - 获取总人数")
    print("   GET  /api/channels - 获取各通道人数")
    print("   GET  /api/edges    - 获取各edge人数")
    print("   GET  /api/status   - 获取服务状态")
    
    print("\n⚡ 模拟数据每3秒自动更新")
    print("🛑 按 Ctrl+C 停止服务")
    print("=" * 60)
    
    # 启动模拟数据更新线程
    update_thread = threading.Thread(target=simulate_person_count_updates, daemon=True)
    update_thread.start()
    
    try:
        # 启动Flask服务器
        app.run(host='0.0.0.0', port=5123, debug=False, threaded=True)
    except KeyboardInterrupt:
        print("\n服务器已停止")
    finally:
        update_running = False
